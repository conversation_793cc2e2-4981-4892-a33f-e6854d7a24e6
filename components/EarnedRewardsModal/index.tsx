import { useEarnedRewardsModal } from '@/context/EarnedRewardsContext';
import { markEarnedRewardAsSeen } from '@/methods/rewards';
import { useQueryClient } from '@tanstack/react-query';
import { useEffect, useRef } from 'react';
import { Animated, Modal, TouchableOpacity, View } from 'react-native';
import Button from '../common/Button';
import {
  CelebrationEmoji,
  ModalContainer,
  ModalContent,
  ModalMessage,
  ModalTitle,
  PointsContainer,
  PointsText
} from './styles';

// Generic Success Modal Props Interface
interface GenericSuccessModalProps {
  isVisible: boolean;
  onClose: () => void;
  emoji?: string;
  title?: string;
  message?: string;
  showPoints?: boolean;
  points?: number;
  buttonText?: string;
}

// Generic Success Modal Component (reuses <PERSON>'s styling and structure)
export const GenericSuccessModal = ({
  isVisible,
  onClose,
  emoji = '🎉',
  title = 'Success!',
  message = 'Action completed successfully!',
  showPoints = false,
  points = 0,
  buttonText = 'Continue'
}: GenericSuccessModalProps) => {
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.3)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;

  useEffect(() => {
    if (isVisible) {
      // Start animations when modal becomes visible
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.spring(scaleAnim, {
          toValue: 1,
          tension: 100,
          friction: 8,
          useNativeDriver: true,
        }),
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      // Reset animations when modal is hidden
      fadeAnim.setValue(0);
      scaleAnim.setValue(0.3);
      slideAnim.setValue(50);
    }
  }, [isVisible, fadeAnim, scaleAnim, slideAnim]);

  const handleClose = () => {
    // Animate out before closing
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 0.3,
        duration: 200,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 50,
        duration: 200,
        useNativeDriver: true,
      }),
    ]).start(() => {
      onClose();
    });
  };

  if (!isVisible) {
    return null;
  }

  return (
    <Modal
      transparent
      visible={isVisible}
      animationType="none"
      onRequestClose={handleClose}
      statusBarTranslucent
    >
      <ModalContainer
        activeOpacity={1}
        onPress={handleClose}
      >
        <Animated.View
          style={{
            position: 'absolute',
            justifyContent: 'center',
            alignItems: 'center',
            transform: [
              { scale: scaleAnim },
              { translateY: slideAnim },
            ],
          }}
        >
          <TouchableOpacity
            activeOpacity={1}
            onPress={(e) => e.stopPropagation()}
          >
            <ModalContent>
              <CelebrationEmoji>{emoji}</CelebrationEmoji>

              <ModalTitle>{title}</ModalTitle>

              <ModalMessage>
                {message}
              </ModalMessage>

              {showPoints && (
                <PointsContainer>
                  <PointsText>+{points} points</PointsText>
                </PointsContainer>
              )}

              <View style={{ flex: 1, alignSelf: 'stretch' }}>
                <Button onPress={handleClose} buttonColor="green" title={buttonText} />
              </View>
            </ModalContent>
          </TouchableOpacity>
        </Animated.View>
      </ModalContainer>
    </Modal>
  );
};

// Original EarnedRewardsModal (unchanged)
export const EarnedRewardsModal = () => {
  const { isModalVisible, currentRewards, hideRewardModal } = useEarnedRewardsModal();
  const queryClient = useQueryClient();
  const markSeenMutation = markEarnedRewardAsSeen();

  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.3)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;

  useEffect(() => {
    if (isModalVisible) {
      // Mark all current rewards as seen when modal loads
      const markRewardsAsSeen = async () => {
        try {
          const markSeenPromises = currentRewards.map(reward =>
            markSeenMutation.mutateAsync({ _id: reward._id })
          );

          await Promise.all(markSeenPromises);

          // Invalidate queries to refetch data
          queryClient.invalidateQueries({ queryKey: ['earnedRewards', 'unseen'] });
        } catch (error) {
          console.error('Error marking rewards as seen:', error);
        }
      };

      markRewardsAsSeen();

      // Animate in
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.spring(scaleAnim, {
          toValue: 1,
          tension: 50,
          friction: 7,
          useNativeDriver: true,
        }),
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 400,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      // Reset animations
      fadeAnim.setValue(0);
      scaleAnim.setValue(0.3);
      slideAnim.setValue(50);
    }
  }, [isModalVisible]);

  const handleClose = () => {
    // Animate out
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 0.3,
        duration: 200,
        useNativeDriver: true,
      }),
    ]).start(() => {
      hideRewardModal();
    });
  };

  if (!isModalVisible) {
    return null;
  }

  const rewardsInfo = currentRewards[0]?.rewardInfo;

  return (
    <Modal
      transparent
      visible={isModalVisible}
      animationType="none"
      onRequestClose={handleClose}
      statusBarTranslucent
    >
      <ModalContainer
        activeOpacity={1}
        onPress={handleClose}
      >
        <Animated.View
          style={{
            position: 'absolute',
            justifyContent: 'center',
            alignItems: 'center',
            transform: [
              { scale: scaleAnim },
              { translateY: slideAnim },
            ],
          }}
        >
          <TouchableOpacity
            activeOpacity={1}
            onPress={(e) => e.stopPropagation()}
          >
            <ModalContent>
              <CelebrationEmoji>🎉</CelebrationEmoji>

              <ModalTitle>{rewardsInfo?.congratsTitle}</ModalTitle>

              <ModalMessage>
                {rewardsInfo?.congratsDescription}
              </ModalMessage>

              <PointsContainer>
                <PointsText>+{rewardsInfo?.points} points</PointsText>
              </PointsContainer>

              <View style={{ flex: 1, alignSelf: 'stretch' }}>
                <Button onPress={handleClose} buttonColor="green" title="Continue" />
              </View>
            </ModalContent>
          </TouchableOpacity>
        </Animated.View>
      </ModalContainer>
    </Modal>
  );
}; 
