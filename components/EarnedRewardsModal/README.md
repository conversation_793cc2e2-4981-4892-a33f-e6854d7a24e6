# EarnedRewardsModal Components

This file contains two modal components:

## 1. EarnedRewardsModal (Original)
The original rewards modal created by <PERSON> - **unchanged and fully functional**.

## 2. GenericSuccessModal (New Reusable Component)
A reusable version that uses <PERSON>'s styling and structure but accepts custom props.

### Usage Examples:

#### For Outfit Saving Success:
```tsx
import { GenericSuccessModal } from '@/components/EarnedRewardsModal';

// In your component:
const [showSuccessModal, setShowSuccessModal] = useState(false);

// After successful outfit save:
<GenericSuccessModal
  isVisible={showSuccessModal}
  onClose={() => setShowSuccessModal(false)}
  emoji="✨"
  title="Outfit Saved!"
  message="Your outfit has been successfully saved to your closet."
  showPoints={false}
  buttonText="Done"
/>
```

#### For Item Added Success:
```tsx
<GenericSuccessModal
  isVisible={showSuccessModal}
  onClose={() => setShowSuccessModal(false)}
  emoji="👗"
  title="Item Added!"
  message="Your new item has been added to your closet."
  showPoints={false}
  buttonText="View Item"
/>
```

#### With Points (like original):
```tsx
<GenericSuccessModal
  isVisible={showSuccessModal}
  onClose={() => setShowSuccessModal(false)}
  emoji="🎉"
  title="Achievement Unlocked!"
  message="You've completed your first outfit!"
  showPoints={true}
  points={25}
  buttonText="Continue"
/>
```

### Props:
- `isVisible`: boolean - Controls modal visibility
- `onClose`: function - Called when modal should close
- `emoji`: string (optional) - Celebration emoji (default: '🎉')
- `title`: string (optional) - Modal title (default: 'Success!')
- `message`: string (optional) - Modal message (default: 'Action completed successfully!')
- `showPoints`: boolean (optional) - Show points section (default: false)
- `points`: number (optional) - Points to display (default: 0)
- `buttonText`: string (optional) - Button text (default: 'Continue')
