# GenericSuccessModal

A reusable success modal component that uses <PERSON>'s EarnedRewardsModal styling and structure but is completely separate to avoid merge conflicts.

## Usage

```tsx
import { GenericSuccessModal } from '@/components/GenericSuccessModal';

// For Outfit Saving Success:
<GenericSuccessModal
  isVisible={showSuccessModal}
  onClose={() => setShowSuccessModal(false)}
  emoji="✨"
  title="Outfit Saved!"
  message="Your outfit has been successfully saved to your closet."
  showPoints={false}
  buttonText="Done"
/>
```

## Props

- `isVisible`: boolean - Controls modal visibility
- `onClose`: function - Called when modal should close
- `emoji`: string (optional) - Celebration emoji (default: '🎉')
- `title`: string (optional) - Modal title (default: 'Success!')
- `message`: string (optional) - Modal message (default: 'Action completed successfully!')
- `showPoints`: boolean (optional) - Show points section (default: false)
- `points`: number (optional) - Points to display (default: 0)
- `buttonText`: string (optional) - Button text (default: 'Continue')

## Features

- ✅ Same beautiful styling and animations as <PERSON>'s EarnedRewardsModal
- ✅ Completely separate component - no merge conflicts
- ✅ Fully customizable for different success scenarios
- ✅ Consistent with app's design system
- ✅ Optional points section for rewards-like functionality
