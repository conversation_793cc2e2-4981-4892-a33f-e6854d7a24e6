import styled from 'styled-components/native';
import { Dimensions, Platform } from 'react-native';
import { PADDING, HEIGHTS, FONT_SIZES, BORDER_RADIUS, isTablet } from '@/constants/responsive';

// Check if device is a tablet
const isTabletDevice = isTablet();

// Get screen dimensions for responsive calculations
const screenWidth = Dimensions.get('window').width;
const contentPadding = PADDING.MODAL_CONTENT_HORIZONTAL * 2;
const itemGap = 15;
const itemWidth = (screenWidth - contentPadding - itemGap) / 2;

// Modal container
export const ModalContainer = styled.View`
  width: 100%;
  height: 80%;
  max-height: ${isTabletDevice ? 750 : 650}px;
  background-color: #F0F0F0;
  border-top-left-radius: ${BORDER_RADIUS.MODAL}px;
  border-top-right-radius: ${BORDER_RADIUS.MODAL}px;
  overflow: hidden;
  flex-direction: column;
`;

// Content container
export const ContentContainer = styled.ScrollView`
  width: 100%;
  padding: 0 ${PADDING.MODAL_CONTENT_HORIZONTAL}px;
  flex: 1;
`;

// Frame container for content
export const FrameContainer = styled.View`
  width: 100%;
  margin-top: ${isTabletDevice ? 32 : 24}px;
  gap: ${isTabletDevice ? 24 : 16}px;
`;

// Form group
export const FormGroup = styled.View`
  width: 100%;
  gap: ${isTabletDevice ? 24 : 16}px;
`;

// Input field container
export const InputFieldContainer = styled.View`
  box-sizing: border-box;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 4px ${PADDING.FIELD_HORIZONTAL}px;
  width: 100%;
  height: ${HEIGHTS.FIELD}px;
  background-color: #F0F0F0;
  border: 1px solid #A1A1A1;
  border-radius: ${BORDER_RADIUS.FIELD_GROUP}px;
`;

// Input field label
export const InputFieldLabel = styled.Text`
  font-family: 'MuktaVaaniSemiBold';
  font-style: normal;
  font-size: ${FONT_SIZES.LABEL}px;
  line-height: 24px;
  letter-spacing: 0.5px;
  color: #333333;
`;

// Date button
export const DateButton = styled.TouchableOpacity`
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 4px 8px;
  width: ${Platform.OS === 'android'
    ? (isTabletDevice ? 140 : 120)
    : (isTabletDevice ? 120 : 101)}px;
  height: ${isTabletDevice ? 36 : 32}px;
  background-color: #0E7E61;
  border-radius: 4px;
`;

// Date button text
export const DateButtonText = styled.Text`
  font-family: 'MuktaVaani';
  font-weight: 400;
  font-size: ${FONT_SIZES.INPUT}px;
  line-height: 24px;
  color: #FFFFFF;
  padding-top: 0;
  margin-top: -2px;
  height: 24px;
`;

// Section title
export const SectionTitle = styled.Text`
  font-family: 'MuktaVaaniSemiBold';
  font-size: ${isTabletDevice ? 22 : 20}px;
  line-height: ${isTabletDevice ? 28 : 24}px;
  color: #333333;
  margin-bottom: 0px;
`;

// Dropdown container
export const DropdownContainer = styled.TouchableOpacity`
  width: 100%;
  height: ${HEIGHTS.FIELD}px;
  border: 1px solid #A1A1A1;
  border-radius: ${BORDER_RADIUS.FIELD_GROUP}px;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 0 ${PADDING.FIELD_HORIZONTAL}px;
  background-color: #F0F0F0;
`;

// Dropdown text
export const DropdownText = styled.Text`
  font-family: 'MuktaVaani';
  font-style: normal;
  font-size: ${FONT_SIZES.INPUT}px;
  line-height: 24px;
  color: #333333;
`;

// Search container
export const SearchContainer = styled.View`
  width: 100%;
  height: ${isTabletDevice ? 56 : 48}px;
  border: 1px solid #C0C0C0;
  border-radius: 99px;
  flex-direction: row;
  align-items: center;
  padding: 0 ${PADDING.FIELD_HORIZONTAL}px;
  margin-top: ${isTabletDevice ? 24 : 16}px;
`;

// Search input
export const SearchInput = styled.TextInput`
  flex: 1;
  height: 100%;
  font-family: 'MuktaVaani';
  font-size: ${isTabletDevice ? 16 : 14}px;
  color: #333333;
  padding: 0;
  margin-left: 8px;
`;

// Filter container
export const FilterContainer = styled.ScrollView`
  width: 100%;
  margin-top: ${isTabletDevice ? 24 : 16}px;
  margin-bottom: ${isTabletDevice ? 24 : 16}px;
`;

// Filter button container
export const FilterButtonsContainer = styled.View`
  flex-direction: row;
  gap: ${isTabletDevice ? 8 : 4}px;
`;

// Filter button
interface FilterButtonProps {
  active: boolean;
}

export const FilterButton = styled.TouchableOpacity<FilterButtonProps>`
  box-sizing: border-box;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 8px 16px;
  gap: 8px;
  height: ${isTabletDevice ? 36 : 32}px;
  border-radius: 8px;
  background-color: ${(props: FilterButtonProps) => (props.active ? '#0E7E61' : 'transparent')};
  border: ${(props: FilterButtonProps) => (props.active ? 'none' : '1px solid #0E7E61')};
  margin-right: ${isTabletDevice ? 8 : 4}px;
  overflow: visible;
`;

// Filter button text
export const FilterButtonText = styled.Text<FilterButtonProps>`
  font-family: 'MuktaVaaniMedium';
  font-size: ${isTabletDevice ? 14 : 12}px;
  line-height: ${isTabletDevice ? 18 : 16}px;
  color: ${(props: FilterButtonProps) => (props.active ? '#FFFFFF' : '#0E7E61')};
  text-align: center;
  padding-top: 1px;
  height: ${isTabletDevice ? 18 : 16}px;
`;

// Clothes grid container
export const ClothesGridContainer = styled.View`
  width: 100%;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: space-between;
  margin-bottom: ${isTabletDevice ? 32 : 24}px;
  padding-bottom: ${isTabletDevice ? 100 : 80}px; /* Add extra padding at the bottom for better scrolling */
  min-height: ${isTabletDevice ? 400 : 300}px; /* Ensure there's enough space for items */
`;

// Clothes item container
export const ClothesItemContainer = styled.TouchableOpacity`
  width: ${itemWidth}px;
  height: ${itemWidth + 4}px;
  background-color: #EBEBEB;
  border-radius: 10px;
  margin-bottom: ${isTabletDevice ? 40 : 36}px; /* Increased to make room for the label */
  position: relative;
  align-items: center;
  justify-content: center;
  padding: 0;
  overflow: visible; /* Changed to visible to show the label */
`;

// Clothes item image
export const ClothesItemImage = styled.Image`
  width: 100%;
  height: 100%;
  background-color: #F0F0F0;
  border-radius: 8px;
  object-fit: cover;
`;

// Item name text
export const ClothItemName = styled.Text`
  font-size: 16px;
  font-family: MuktaVaani;
  text-align: left;
  width: 100%;
  margin-top: 8px;
  padding-horizontal: 0;
  color: #333333;
  height: 24px;
  overflow: visible;
  position: absolute;
  bottom: -28px;
  left: 0;
`;

// Checkbox container
export const CheckboxContainer = styled.TouchableOpacity`
  position: absolute;
  bottom: ${isTabletDevice ? 12 : 8}px;
  right: ${isTabletDevice ? 12 : 8}px;
  width: ${isTabletDevice ? 28 : 24}px;
  height: ${isTabletDevice ? 28 : 24}px;
  background-color: #FFFFFF;
  border-radius: ${isTabletDevice ? 6 : 4}px;
  border: 1px solid #067A46;
  align-items: center;
  justify-content: center;
`;

// Modal header
export const ModalHeader = styled.View`
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: ${isTabletDevice ? 24 : 16}px ${PADDING.MODAL_CONTENT_HORIZONTAL}px 0px;
  width: 100%;
  height: ${isTabletDevice ? 80 : 56}px;
`;

// Header button
export const HeaderButton = styled.TouchableOpacity`
  height: ${isTabletDevice ? 48 : 40}px;
  padding: ${isTabletDevice ? 12 : 10}px 0;
  justify-content: center;
`;

// Header button text
export const HeaderButtonText = styled.Text`
  font-family: 'MuktaVaaniMedium';
  font-style: normal;
  font-size: ${FONT_SIZES.LABEL}px;
  line-height: ${isTabletDevice ? 24 : 20}px;
  text-align: center;
  color: #0E7E61;
`;

// Header title
export const HeaderTitle = styled.Text`
  font-family: 'MuktaVaaniSemiBold';
  font-size: ${FONT_SIZES.HEADER}px;
  line-height: ${isTabletDevice ? 32 : 28}px;
  color: #1C1C1C;
  text-align: center;
`;

// Bar indicator at the top of the modal
export const BarIndicatorContainer = styled.View`
  width: 100%;
  height: ${isTabletDevice ? 40 : 34}px;
  align-items: center;
  justify-content: center;
`;

export const BarIndicator = styled.View`
  width: ${isTabletDevice ? 64 : 56}px;
  height: ${isTabletDevice ? 6 : 5}px;
  border-radius: ${isTabletDevice ? 6 : 5}px;
  background-color: rgba(51, 51, 51, 0.2);
`;

// Checkbox checked icon
export const CheckboxChecked = styled.View`
  width: ${isTabletDevice ? 16 : 14}px;
  height: ${isTabletDevice ? 16 : 14}px;
  background-color: #067A46;
  border-radius: ${isTabletDevice ? 3 : 2}px;
`;
